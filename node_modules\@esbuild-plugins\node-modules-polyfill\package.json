{"name": "@esbuild-plugins/node-modules-polyfill", "version": "0.2.2", "description": "", "main": "dist/index.js", "module": "esm/index.js", "types": "dist/index.d.ts", "repository": "https://github.com/remorses/esbuild-plugins.git", "scripts": {"build": "tsc && tsc -m es6 --outDir esm", "watch": "tsc -w"}, "files": ["dist", "src", "esm"], "keywords": [], "author": "<PERSON><PERSON><PERSON>, morse <<EMAIL>>", "license": "ISC", "devDependencies": {"safe-buffer": "^5.2.1", "test-support": "*", "@esbuild-plugins/node-globals-polyfill": "*"}, "dependencies": {"escape-string-regexp": "^4.0.0", "rollup-plugin-node-polyfills": "^0.2.1"}, "peerDependencies": {"esbuild": "*"}}