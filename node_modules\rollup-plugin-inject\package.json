{"name": "rollup-plugin-inject", "description": "Scan modules for global variables and inject `import` statements where necessary", "version": "3.0.2", "devDependencies": {"eslint": "^6.3.0", "mocha": "^6.2.0", "prettier": "^1.18.2", "rollup": "^1.17.0", "shx": "^0.3.2"}, "main": "dist/rollup-plugin-inject.cjs.js", "module": "dist/rollup-plugin-inject.es6.js", "jsnext:main": "dist/rollup-plugin-inject.es6.js", "scripts": {"pretest": "npm run build", "test": "mocha", "prebuild": "shx rm -rf dist", "build": "rollup -c", "prepublishOnly": "npm run lint && npm run test", "prepare": "npm run build", "lint": "eslint --fix src test/test.js"}, "files": ["src", "dist", "README.md"], "dependencies": {"estree-walker": "^0.6.1", "magic-string": "^0.25.3", "rollup-pluginutils": "^2.8.1"}, "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup-plugin-inject.git"}, "keywords": ["rollup", "rollup-plugin", "es2015", "npm", "modules"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/rollup/rollup-plugin-inject/issues"}, "homepage": "https://github.com/rollup/rollup-plugin-inject#readme"}