export { <PERSON><PERSON><PERSON>, Cipher<PERSON>, Decipher, Decipheriv, ECDH, Sign, Verify, constants, createCipheriv, createDecipher<PERSON>, createECDH, createSign, createVerify, diffie<PERSON><PERSON><PERSON>, getCipherInfo, hash, privateDecrypt, privateEncrypt, publicDecrypt, publicEncrypt, sign, verify, } from "unenv/node/crypto";
export declare const Certificate: typeof import("crypto").Certificate, DiffieHellman: typeof import("crypto").<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DiffieHellmanGroup: import("crypto").DiffieHellmanGroupConstructor, Hash: typeof import("crypto").Hash, Hmac: typeof import("crypto").Hmac, KeyObject: typeof import("crypto").KeyObject, X509Certificate: typeof import("crypto").X509Certificate, checkPrime: typeof import("crypto").checkPrime, checkPrimeSync: typeof import("crypto").checkPrimeSync, createD<PERSON><PERSON><PERSON><PERSON>man: typeof import("crypto").createD<PERSON><PERSON><PERSON><PERSON><PERSON>, createDiffieHellmanGroup: typeof import("crypto").createDiffieHellmanGroup, createHash: typeof import("crypto").createHash, createHmac: typeof import("crypto").createHmac, createPrivateKey: typeof import("crypto").createPrivateKey, createPublicKey: typeof import("crypto").createPublicKey, createSecretKey: typeof import("crypto").createSecretKey, generateKey: typeof import("crypto").generateKey, generateKeyPair: typeof import("crypto").generateKeyPair, generateKeyPairSync: typeof import("crypto").generateKeyPairSync, generateKeySync: typeof import("crypto").generateKeySync, generatePrime: typeof import("crypto").generatePrime, generatePrimeSync: typeof import("crypto").generatePrimeSync, getCiphers: typeof import("crypto").getCiphers, getCurves: typeof import("crypto").getCurves, getDiffieHellman: typeof import("crypto").getDiffieHellman, getFips: typeof import("crypto").getFips, getHashes: typeof import("crypto").getHashes, hkdf: typeof import("crypto").hkdf, hkdfSync: typeof import("crypto").hkdfSync, pbkdf2: typeof import("crypto").pbkdf2, pbkdf2Sync: typeof import("crypto").pbkdf2Sync, randomBytes: typeof import("crypto").randomBytes, randomFill: typeof import("crypto").randomFill, randomFillSync: typeof import("crypto").randomFillSync, randomInt: typeof import("crypto").randomInt, randomUUID: typeof import("crypto").randomUUID, scrypt: typeof import("crypto").scrypt, scryptSync: typeof import("crypto").scryptSync, secureHeapUsed: typeof import("crypto").secureHeapUsed, setEngine: typeof import("crypto").setEngine, setFips: typeof import("crypto").setFips, subtle: import("crypto").webcrypto.SubtleCrypto, timingSafeEqual: typeof import("crypto").timingSafeEqual;
export declare const getRandomValues: any;
export declare const webcrypto: any;
declare const _default: any;
export default _default;
